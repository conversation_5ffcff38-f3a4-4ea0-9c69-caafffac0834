
import React from 'react';
import type { Topic } from '../types';
import { Card } from './common/Card';
import { Button } from './common/Button';

interface TopicListProps {
  topics: Topic[];
  onSelectTopic: (topic: Topic) => void;
}

export const TopicList: React.FC<TopicListProps> = ({ topics, onSelectTopic }) => {
  if (!topics || topics.length === 0) {
    return <p className="text-slate-400 italic">No topics extracted yet, or analysis is in progress.</p>;
  }

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-sky-300 border-b border-sky-700 pb-2">Key Topics</h3>
      <div className="max-h-96 overflow-y-auto space-y-3 pr-2">
        {topics.map((topic) => (
          <Card key={topic.id} className="bg-slate-700 hover:bg-slate-600 transition-colors">
            <h4 className="text-lg font-semibold text-sky-400">{topic.title}</h4>
            <p className="text-sm text-slate-300 mt-1 mb-3">{topic.summary}</p>
            <Button onClick={() => onSelectTopic(topic)} className="w-full text-sm bg-cyan-600 hover:bg-cyan-500">
              Generate Presentation on this Topic
            </Button>
          </Card>
        ))}
      </div>
    </div>
  );
};
    