
import React from 'react';
import { Button } from './common/Button'; // Assuming Button component exists

interface VapiButtonProps {
  onClick: () => void;
  isVapiActive: boolean;
}

// Simple SVG Icon for Vapi-like functionality (e.g., a stylized voice icon)
const VapiIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={`w-5 h-5 ${className}`}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
  </svg>
);


export const VapiButton: React.FC<VapiButtonProps> = ({ onClick, isVapiActive }) => {
  return (
    <Button
      onClick={onClick}
      className={`w-full flex items-center justify-center ${
        isVapiActive ? 'bg-red-600 hover:bg-red-500' : 'bg-purple-600 hover:bg-purple-500'
      }`}
      title={isVapiActive ? "Stop Voice AI Conversation (VAPI.ai Simulated)" : "Start Voice AI Conversation (VAPI.ai Simulated)"}
    >
      <VapiIcon className="mr-2"/>
      {isVapiActive ? 'Stop VAPI.ai (Simulated)' : 'Start VAPI.ai (Simulated)'}
    </Button>
  );
};
    