export interface Topic {
  id: string;
  title: string;
  summary: string;
}

export interface SlideContentPoint {
  text: string;
  subPoints?: string[];
}

export interface InteractionType {
  type: 'quiz' | 'video' | 'audio';
  question?: string;
  options?: string[];
  correctAnswer?: string;
  videoUrl?: string;
  audioUrl?: string;
}

export interface Slide {
  slideTitle: string;
  contentPoints: string[]; // Was: SlideContentPoint[], simplified to string array for current Gemini output
  imageSuggestion?: string | null; // Prompt for image
  diagramSuggestion?: { type: string; description: string } | null;
  imageUrl?: string; // Actual URL of generated/placeholder image
  mermaidSyntax?: string; // Mermaid code
  interaction?: InteractionType;
}

export interface Presentation {
  title: string;
  slides: Slide[];
}

export interface GeminiChatSession {
  // Define if using more complex chat features with history.
  // For simple send/receive, this might not be complex.
  sendMessage: (message: string) => Promise<string>;
}

export interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

// Speech Recognition API Types

export interface SpeechRecognitionAlternative {
  readonly transcript: string;
  readonly confidence: number;
}

export interface SpeechRecognitionResult {
  readonly isFinal: boolean;
  readonly length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
}

export interface SpeechRecognitionResultList {
  readonly length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

export interface SpeechRecognitionEvent extends Event {
  readonly resultIndex: number;
  readonly results: SpeechRecognitionResultList;
}

export interface SpeechRecognitionErrorEvent extends Event {
  readonly error: string; 
  readonly message: string;
}

export interface SpeechRecognitionStatic {
  new(): SpeechRecognition;
}

export interface SpeechRecognition extends EventTarget {
  lang: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
  grammars?: any; // Replace 'any' with 'SpeechGrammarList' if defined and used

  onaudiostart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onaudioend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any) | null;
  onnomatch: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onsoundstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onsoundend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;

  abort(): void;
  start(): void;
  stop(): void;
}

// Declare the global variables for the constructors, making them available directly.
// The 'undefined' acknowledges they might not be present in all browsers/environments.
declare var SpeechRecognition: SpeechRecognitionStatic | undefined;
declare var webkitSpeechRecognition: SpeechRecognitionStatic | undefined;

// Augment the Window interface for cases where the API is accessed via `window.SpeechRecognition`.
// This complements the `declare var` for comprehensive type coverage.
declare global {
  interface Window {
    SpeechRecognition?: SpeechRecognitionStatic;
    webkitSpeechRecognition?: SpeechRecognitionStatic;
  }
}