
import React, { useCallback } from 'react';

interface BookViewerProps {
  content: string;
  onTextSelect: (selectedText: string) => void;
}

export const BookViewer: React.FC<BookViewerProps> = ({ content, onTextSelect }) => {
  const handleMouseUp = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim().length > 0) {
      onTextSelect(selection.toString().trim());
    }
  }, [onTextSelect]);

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-semibold text-sky-300 border-b border-sky-700 pb-2">Book Content</h2>
      <div 
        onMouseUp={handleMouseUp}
        className="prose prose-invert max-w-none bg-slate-700 p-6 rounded-lg shadow text-slate-200 whitespace-pre-wrap overflow-y-auto h-[calc(100vh-350px)] md:h-[calc(100vh-300px)] selection:bg-sky-500 selection:text-white"
        style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}
      >
        {content || "No content loaded."}
      </div>
      <p className="text-sm text-slate-400 italic mt-2">Select text above to interact with it (read aloud, chat).</p>
    </div>
  );
};
    