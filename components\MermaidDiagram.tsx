
import React, { useEffect, useRef } from 'react';

interface MermaidDiagramProps {
  chart: string;
  id: string; // Unique ID for the diagram container
}

export const MermaidDiagram: React.FC<MermaidDiagramProps> = ({ chart, id }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current && window.mermaid) {
      containerRef.current.innerHTML = ''; // Clear previous diagram
      try {
        window.mermaid.render(id + '-svg', chart, (svgCode: string) => {
          if (containerRef.current) {
            containerRef.current.innerHTML = svgCode;
            // Ensure mermaid styles apply correctly if theme changes etc.
            // This might be needed if themes are dynamically changed.
            // window.mermaid.init(undefined, containerRef.current.querySelectorAll('.mermaid'));
          }
        });
      } catch (e) {
        console.error("Error rendering Mermaid diagram:", e);
        if (containerRef.current) {
            containerRef.current.innerHTML = `<pre class="text-red-400">Error rendering diagram: ${ (e as Error).message }</pre>`;
        }
      }
    }
  }, [chart, id]);

  return (
    <div className="my-4 p-4 bg-slate-600 rounded-lg overflow-x-auto">
      <h4 className="text-md font-semibold text-sky-300 mb-2">Diagram:</h4>
      <div ref={containerRef} id={id} className="mermaid text-slate-100 flex justify-center">
        {/* Mermaid will render SVG here */}
      </div>
       <p className="text-xs text-slate-400 mt-2 italic">If diagram is not showing, check console for errors.</p>
    </div>
  );
};
    