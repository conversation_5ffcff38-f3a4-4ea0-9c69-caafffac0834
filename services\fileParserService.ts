
// Ensure pdfjsLib and mammoth are available globally from CDN
declare global {
  interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    pdfjsLib?: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mammoth?: any;
  }
}

export const parseFile = async (file: File): Promise<string> => {
  const fileName = file.name.toLowerCase();
  const fileType = file.type;

  if (fileName.endsWith('.txt') || fileType === 'text/plain') {
    return file.text();
  } else if (fileName.endsWith('.pdf') || fileType === 'application/pdf') {
    if (!window.pdfjsLib) {
      throw new Error("pdf.js library is not loaded. Please ensure it's included via CDN.");
    }
    // pdf.js workerSrc should be set globally, e.g., in index.html or App.tsx
    // window.pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${window.pdfjsLib.version}/pdf.worker.min.js`;

    const arrayBuffer = await file.arrayBuffer();
    const pdf = await window.pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    let textContent = '';
    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const text = await page.getTextContent();
      textContent += text.items.map((item: any) => item.str).join(' ') + '\n'; // Type any for item
    }
    return textContent;
  } else if (fileName.endsWith('.docx') || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    if (!window.mammoth) {
        throw new Error("Mammoth.js library is not loaded. Please ensure it's included via CDN.");
    }
    const arrayBuffer = await file.arrayBuffer();
    const result = await window.mammoth.extractRawText({ arrayBuffer });
    return result.value;
  } else {
    throw new Error(`Unsupported file type: ${fileName}. Please upload TXT, PDF, or DOCX.`);
  }
};
    