
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger';
}

export const Button: React.FC<ButtonProps> = ({ children, className, variant = 'primary', ...props }) => {
  const baseStyles = "px-4 py-2 font-semibold rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-75 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150";
  
  let variantStyles = "";
  switch (variant) {
    case 'secondary':
      variantStyles = "bg-slate-600 hover:bg-slate-500 text-white focus:ring-slate-400";
      break;
    case 'danger':
      variantStyles = "bg-red-600 hover:bg-red-500 text-white focus:ring-red-400";
      break;
    case 'primary':
    default:
      // Default styles are often applied directly or via className prop for more flexibility
      // For example, bg-sky-600 hover:bg-sky-500 text-white focus:ring-sky-400
      // If className is not provided with bg, it might be transparent.
      // Let's ensure a default if no bg is in className
      variantStyles = props.disabled ? "bg-gray-500" : "bg-sky-600 hover:bg-sky-500 text-white focus:ring-sky-400";
      if (className && className.includes('bg-')) {
        variantStyles = ""; // User is providing their own background
      }
      break;
  }

  return (
    <button
      className={`${baseStyles} ${variantStyles} ${className || ''}`}
      {...props}
    >
      {children}
    </button>
  );
};
    